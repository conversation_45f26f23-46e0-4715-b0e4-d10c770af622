import { world, InputPermissionCategory, Player, PlayerSpawnAfterEvent, system } from "@minecraft/server";
import { giveGuidebookToPlayer } from "../guidebook";

/**
 * Restores all input permissions for a player
 * This is particularly useful when a player rejoins the game after leaving while under a stun effect
 *
 * @param player The player to restore permissions for
 */
export function restorePlayerInputPermissions(player: Player): void {
  try {
    // Restore movement permission
    player.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, true);

    // Restore camera permission (in case it was also disabled)
    player.inputPermissions.setPermissionCategory(InputPermissionCategory.Camera, true);
  } catch (error) {
    console.warn(`Failed to restore input permissions for player: ${player.name}. Error: ${error}`);
  }
}

/**
 * Handler for player spawn events
 * Restores input permissions when a player joins or respawns
 * Also gives the guidebook to new players after a delay
 *
 * @param event The player spawn event
 */
function handlePlayerSpawn(event: PlayerSpawnAfterEvent): void {
  const player = event.player;

  // Restore input permissions for the spawned player
  restorePlayerInputPermissions(player);

  // Give guidebook to player after a delay to ensure they're fully loaded
  // Only give on initial spawn (not respawn after death)
  if (event.initialSpawn) {
    system.runTimeout(() => {
      try {
        giveGuidebookToPlayer(player);
      } catch (error) {
        console.warn(`Failed to give guidebook to player ${player.name}: ${error}`);
      }
    }, 40); // Wait 2 seconds (40 ticks) for player to fully load
  }
}

// Subscribe to the playerSpawn event
world.afterEvents.playerSpawn.subscribe(handlePlayerSpawn);
