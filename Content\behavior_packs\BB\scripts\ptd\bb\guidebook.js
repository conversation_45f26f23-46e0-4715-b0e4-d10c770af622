import { ItemStack, EntityComponentTypes } from "@minecraft/server";
import { ActionFormData } from "@minecraft/server-ui";
const GUIDEBOOK_SECTIONS = [
    {
        title: "Introduction",
        content: [
            "Face epic new battles with Better Bosses!",
            "",
            "Discover powerful foes across the Overworld, Nether, and End, each with unique abilities, animations, and mechanics.",
            "",
            "Survive their wrath, claim rewards, and prepare for more bosses to come!"
        ]
    },
    {
        title: "The Piglin Champion",
        content: [
            "The Piglin Champion is a towering piglin warrior armed with a massive golden axe.",
            "",
            "This boss features multiple attack phases, can summon minions to aid in battle, and becomes increasingly dangerous as the fight progresses.",
            "",
            "Health Points: 1,800 HP"
        ]
    },
    {
        title: "Summoning Requirements",
        content: [
            "To summon the Piglin Champion, you'll need:",
            "",
            "• 1× Gold Block (base item - place this first)",
            "• 1× Gold Ingot",
            "• 1× Golden Axe",
            "• 1× Enchanted Golden Apple",
            "• 1× Honey Bottle",
            "",
            "How to Summon:",
            "1. Drop the Gold Block on the ground",
            "2. Drop the other 4 items within 2 blocks of the Gold Block",
            "3. The Piglin Champion will automatically spawn when all items are detected",
            "4. All summoning items will be consumed in the process"
        ]
    },
    {
        title: "Boss Attacks",
        content: [
            "The Piglin Champion has various devastating attacks:",
            "",
            "• Horizontal Slash - 16 damage",
            "• Vertical Axe Attack - 16 damage (axe), 8 damage (rock fissure)",
            "• Foot Stomp - 12 damage with area effect",
            "• Spin Slam - 12 damage with knockback",
            "• Body Slam - 18 damage (highest damage attack)",
            "• Charging Attack - 7 damage with movement",
            "• Healing Ability - Can regenerate health during combat",
            "• Minion Summoning - Summons Piglin Brutes and Piglin Marauders"
        ]
    },
    {
        title: "Minions",
        content: [
            "The Piglin Champion can summon two types of minions:",
            "",
            "Piglin Brute:",
            "• Moderate HP",
            "• Vertical and horizontal strikes",
            "• Aggressive melee fighter",
            "",
            "Piglin Marauder:",
            "• Moderate HP",
            "• Slam and sweep attacks",
            "• Mobile attacker with area damage"
        ]
    },
    {
        title: "Exclusive Items",
        content: [
            "Piglin Champion Goblet:",
            "• Durability: 800",
            "• Cooldown: 30 seconds",
            "• Effect: Regeneration V for 5 seconds",
            "• Repair Item: Gold Scrap (restores 10 durability)",
            "",
            "Piglin Champion Axe:",
            "• Durability: 1,561",
            "• Damage: 8 attack damage",
            "• Cooldown: 15 seconds",
            "• Special: Vertical ground slam attack",
            "• Repair Item: Gold Scrap (restores 20 durability)"
        ]
    },
    {
        title: "Armor Set",
        content: [
            "Piglin Champion Armor Set provides:",
            "",
            "• Helmet: 1 protection, 165 durability",
            "• Chestplate: 5 protection, 240 durability",
            "• Leggings: 4 protection, 125 durability",
            "• Boots: 2 protection, 240 durability",
            "",
            "Total Protection: 12 armor points",
            "Repair Item: Gold Scrap (restores 35 durability per piece)",
            "",
            "Special Ability: Crouch slam attack",
            "• Crouch for 0.5 seconds to perform a powerful slam",
            "• Knocks back and damages nearby enemies",
            "• Cooldown: 5 seconds"
        ]
    },
    {
        title: "Combat Tips",
        content: [
            "Preparation:",
            "• Bring plenty of food - The fight is long and demanding",
            "• Enchanted weapons recommended",
            "• Shield or armor essential for survival",
            "• Keep healing potions ready",
            "",
            "Battle Strategy:",
            "• Watch for attack patterns - Each attack has distinct animations",
            "• Manage minions first - Deal with summoned enemies quickly",
            "• Use the environment - Keep distance during ranged phases",
            "• Save the Goblet - Use for emergency healing",
            "",
            "Attack Warnings:",
            "• Body Slam - Highest damage (18) - dodge the windup",
            "• Foot Stomp - Area effect - get out of range quickly",
            "• Minion Summoning - Prioritize killing minions"
        ]
    }
];
/**
 * Displays the main guidebook menu to the player
 * @param player The player to show the guidebook to
 */
export async function showGuidebook(player) {
    try {
        const form = new ActionFormData()
            .title("§6§lBetter Bosses Guidebook")
            .body("§7Select a section to learn more about the bosses and their mechanics:");
        // Add buttons for each section
        GUIDEBOOK_SECTIONS.forEach(section => {
            form.button(section.title);
        });
        // Add close button
        form.button("§cClose Guidebook");
        const response = await form.show(player);
        if (response.canceled || response.selection === undefined) {
            return;
        }
        // Handle close button (last button)
        if (response.selection === GUIDEBOOK_SECTIONS.length) {
            return;
        }
        // Show the selected section
        if (response.selection >= 0 && response.selection < GUIDEBOOK_SECTIONS.length) {
            await showGuidebookSection(player, response.selection);
        }
    }
    catch (error) {
        console.warn(`Error showing guidebook: ${error}`);
    }
}
/**
 * Displays a specific section of the guidebook
 * @param player The player to show the section to
 * @param sectionIndex The index of the section to show
 */
async function showGuidebookSection(player, sectionIndex) {
    try {
        const section = GUIDEBOOK_SECTIONS[sectionIndex];
        if (!section)
            return;
        const form = new ActionFormData()
            .title(`§6§l${section.title}`)
            .body(section.content.join("\n"));
        // Add navigation buttons
        if (sectionIndex > 0) {
            form.button("§e← Previous Section");
        }
        if (sectionIndex < GUIDEBOOK_SECTIONS.length - 1) {
            form.button("§e→ Next Section");
        }
        form.button("§9↑ Back to Menu");
        form.button("§cClose Guidebook");
        const response = await form.show(player);
        if (response.canceled || response.selection === undefined) {
            return;
        }
        let buttonIndex = 0;
        // Handle Previous Section button
        if (sectionIndex > 0) {
            if (response.selection === buttonIndex) {
                await showGuidebookSection(player, sectionIndex - 1);
                return;
            }
            buttonIndex++;
        }
        // Handle Next Section button
        if (sectionIndex < GUIDEBOOK_SECTIONS.length - 1) {
            if (response.selection === buttonIndex) {
                await showGuidebookSection(player, sectionIndex + 1);
                return;
            }
            buttonIndex++;
        }
        // Handle Back to Menu button
        if (response.selection === buttonIndex) {
            await showGuidebook(player);
            return;
        }
        // Close button is handled by default (no action needed)
    }
    catch (error) {
        console.warn(`Error showing guidebook section: ${error}`);
    }
}
/**
 * Handles the guidebook item usage
 * @param player The player who used the guidebook
 * @param item The guidebook ItemStack
 */
export function guidebookOnUse(player, item) {
    try {
        // Show the guidebook UI
        showGuidebook(player);
    }
    catch (error) {
        console.warn(`Error in guidebookOnUse: ${error}`);
    }
}
/**
 * Checks if a player has the guidebook in their inventory
 * @param player The player to check
 * @returns True if the player has the guidebook, false otherwise
 */
export function playerHasGuidebook(player) {
    try {
        const inventoryComponent = player.getComponent(EntityComponentTypes.Inventory);
        if (!inventoryComponent)
            return false;
        const container = inventoryComponent.container;
        if (!container)
            return false;
        // Check all inventory slots for the guidebook
        for (let i = 0; i < container.size; i++) {
            const item = container.getItem(i);
            if (item && item.type.id === "ptd_bb:guidebook") {
                return true;
            }
        }
        return false;
    }
    catch (error) {
        console.warn(`Error checking if player has guidebook: ${error}`);
        return false;
    }
}
/**
 * Gives the guidebook to a player if they don't already have it
 * @param player The player to give the guidebook to
 */
export function giveGuidebookToPlayer(player) {
    try {
        // Check if player already has the guidebook
        if (playerHasGuidebook(player)) {
            return;
        }
        const inventoryComponent = player.getComponent(EntityComponentTypes.Inventory);
        if (!inventoryComponent)
            return;
        const container = inventoryComponent.container;
        if (!container)
            return;
        // Create the guidebook item
        const guidebook = new ItemStack("ptd_bb:guidebook", 1);
        // Try to add the guidebook to the player's inventory
        const remainingItem = container.addItem(guidebook);
        // If inventory is full, drop it at the player's location
        if (remainingItem) {
            player.dimension.spawnItem(remainingItem, player.location);
            player.sendMessage("§6[Better Bosses] §7Your inventory is full! The guidebook was dropped at your feet.");
        }
        else {
            player.sendMessage("§6[Better Bosses] §7Welcome! You've received the Better Bosses Guidebook. Use it to learn about bosses and their mechanics!");
        }
    }
    catch (error) {
        console.warn(`Error giving guidebook to player: ${error}`);
    }
}
